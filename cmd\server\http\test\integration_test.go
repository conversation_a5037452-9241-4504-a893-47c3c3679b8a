package test

import (
	"bytes"
	"encoding/json"
	"log"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	ziaHttp "ziaacademy-backend/cmd/server/http"
	dbPkg "ziaacademy-backend/db"
	"ziaacademy-backend/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var db *gorm.DB
var router, noAuthRouter *gin.Engine

func cleanupTestData() {
	// Clean up test data to avoid unique constraint violations
	// Delete in order to respect foreign key constraints
	db.Exec("DELETE FROM sections WHERE test_id IN (SELECT id FROM tests WHERE test_type_id IN (SELECT id FROM test_types WHERE name LIKE '%Mock%' OR name LIKE 'Test %'))")
	db.Exec("DELETE FROM tests WHERE test_type_id IN (SELECT id FROM test_types WHERE name LIKE '%Mock%' OR name LIKE 'Test %')")
	db.Exec("DELETE FROM videos WHERE name LIKE 'test_%'")
	db.Exec("DELETE FROM chapters WHERE name LIKE 'Test %'")
	db.Exec("DELETE FROM test_types WHERE name LIKE '%Mock%' OR name LIKE 'Test %'")
	db.Exec("DELETE FROM section_types WHERE name LIKE '%Section' OR name LIKE 'Test %'")
	db.Exec("DELETE FROM formula_cards WHERE subject_id IN (SELECT id FROM subjects WHERE name LIKE 'Test %' OR name = 'Mathematics' OR name = 'Chemistry 12th' OR name = 'Physics Test' OR name LIKE '% Test Formula')")
	db.Exec("DELETE FROM previous_year_papers WHERE exam_type IN ('IIT-JEE', 'NEET')")
	db.Exec("DELETE FROM subjects WHERE name LIKE 'Test %' OR name = 'Mathematics' OR name = 'Chemistry 12th' OR name = 'Physics Test' OR name LIKE '% Test Formula'")
	db.Exec("DELETE FROM users WHERE email LIKE '%@example.com'")

	// Clean up transaction-related test data
	CleanupTransactionTestData()
}

func TestMain(m *testing.M) {
	dsn := "host=localhost user=postgres password=postgres dbname=mydatabase port=5432 sslmode=disable"
	var err error
	db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to DB: %v", err)
	}

	server := dbPkg.NewServer(db)

	// Setup router with skipAuth=true for testing
	router = ziaHttp.SetupRouter(server, os.Stdout, false)
	noAuthRouter = ziaHttp.SetupRouter(server, os.Stdout, true)

	// Clean up test data before running tests
	cleanupTestData()

	code := m.Run()

	// Optional: cleanup
	sqlDB, _ := db.DB()
	sqlDB.Close()
	os.Exit(code)
}

func TestCreateVideo(t *testing.T) {
	t.Skip("Skipping integration test")
	// Clean up before test
	db.Exec("DELETE FROM videos WHERE name = ?", "test_video")

	// First create a subject and chapter for the video
	subject := models.Subject{
		Name:        "Test Subject",
		DisplayName: "Test Subject Display",
	}
	db.Create(&subject)

	chapter := models.Chapter{
		Name:        "Test Chapter",
		DisplayName: "Test Chapter Display",
		SubjectID:   subject.ID,
	}
	db.Create(&chapter)

	videoInput := models.VideoForCreate{
		Name:        "test_video",
		DisplayName: "Test Video Display",
		VideoUrl:    "http://example.com/video.mp4",
		ChapterName: "Test Chapter",
	}

	body, _ := json.Marshal(videoInput)
	req, _ := http.NewRequest("POST", "/api/videos", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	resp := httptest.NewRecorder()
	noAuthRouter.ServeHTTP(resp, req)

	assert.Equal(t, http.StatusOK, resp.Code)

	// Verify entry in DB
	var savedVideo models.Video
	result := db.First(&savedVideo, "name = ?", "test_video")
	assert.Nil(t, result.Error)
	assert.Equal(t, videoInput.DisplayName, savedVideo.DisplayName)
	assert.Equal(t, videoInput.VideoUrl, savedVideo.VideoUrl)

	// Cleanup
	db.Delete(&savedVideo)
	db.Delete(&chapter)
	db.Delete(&subject)
}

func TestCreateStudentWithCourses(t *testing.T) {
	// Clean up before test
	db.Exec("DELETE FROM students WHERE parent_email = ?", "<EMAIL>")
	db.Exec("DELETE FROM users WHERE email = ?", "<EMAIL>")

	// Build student payload using the correct structure with enhanced fields
	payload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Student User",
			Email:          "<EMAIL>",
			PhoneNumber:    "1234567896",
			ContactAddress: "123 Student Street",
		},
		ParentPhone: "555-1234",
		ParentEmail: "<EMAIL>",
		// Enhanced student information
		Institute:  "Test International School",
		Class:      "11th",
		Stream:     "NEET",
		CityOrTown: "Delhi",
		State:      "Delhi",
	}
	body, _ := json.Marshal(payload)

	// Prepare request
	req, _ := http.NewRequest("POST", "/api/students", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")

	resp := httptest.NewRecorder()
	noAuthRouter.ServeHTTP(resp, req)

	var res models.CreatedStudentResponse
	err := json.NewDecoder(resp.Body).Decode(&res)
	assert.NoError(t, err)
	// Assertions
	assert.Equal(t, http.StatusOK, resp.Code)

	// Verify: Student was created with all fields including enhanced fields
	var student models.Student
	err = db.Preload("User").First(&student, "id = ?", res.Student.ID).Error
	assert.Nil(t, err)

	// Verify basic student fields
	assert.Equal(t, "<EMAIL>", student.ParentEmail)
	assert.Equal(t, "555-1234", student.ParentPhone)
	assert.Equal(t, "Student User", student.User.FullName)
	assert.Equal(t, "<EMAIL>", student.User.Email)
	assert.Equal(t, "1234567896", student.User.PhoneNumber)
	assert.Equal(t, "123 Student Street", student.User.ContactAddress)

	// Verify enhanced student fields
	assert.Equal(t, "Test International School", student.Institute)
	assert.Equal(t, "11th", student.Class)
	assert.Equal(t, "NEET", student.Stream)
	assert.Equal(t, "Delhi", student.CityOrTown)
	assert.Equal(t, "Delhi", student.State)

	// Verify response contains simple fields (ID and name)
	assert.Equal(t, student.ID, res.Student.ID)
	assert.Equal(t, student.User.FullName, res.Student.Name)

	// Verify enhanced fields were saved correctly in database
	assert.Equal(t, "Test International School", student.Institute)
	assert.Equal(t, "11th", student.Class)
	assert.Equal(t, "NEET", student.Stream)
	assert.Equal(t, "Delhi", student.CityOrTown)
	assert.Equal(t, "Delhi", student.State)

	// Test 2: Create student with minimal fields (enhanced fields empty)
	db.Exec("DELETE FROM students WHERE parent_email = ?", "<EMAIL>")
	db.Exec("DELETE FROM users WHERE email = ?", "<EMAIL>")

	minimalPayload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Minimal Student",
			Email:          "<EMAIL>",
			PhoneNumber:    "9876543216",
			ContactAddress: "456 Minimal Street",
		},
		ParentPhone: "555-5678",
		ParentEmail: "<EMAIL>",
		// Enhanced fields left empty to test optional nature
	}

	minimalBody, _ := json.Marshal(minimalPayload)
	minimalReq, _ := http.NewRequest("POST", "/api/students", bytes.NewBuffer(minimalBody))
	minimalReq.Header.Set("Content-Type", "application/json")

	minimalResp := httptest.NewRecorder()
	noAuthRouter.ServeHTTP(minimalResp, minimalReq)

	var minimalRes models.CreatedStudentResponse
	err = json.NewDecoder(minimalResp.Body).Decode(&minimalRes)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, minimalResp.Code)

	// Verify minimal student creation works with empty enhanced fields
	var minimalStudent models.Student
	err = db.Preload("User").First(&minimalStudent, "id = ?", minimalRes.Student.ID).Error
	assert.Nil(t, err)
	assert.Equal(t, "Minimal Student", minimalStudent.User.FullName)
	assert.Equal(t, "", minimalStudent.Institute)
	assert.Equal(t, "", minimalStudent.Class)
	assert.Equal(t, "", minimalStudent.Stream)
	assert.Equal(t, "", minimalStudent.CityOrTown)
	assert.Equal(t, "", minimalStudent.State)

	// Test 3: Test validation - invalid class value
	invalidClassPayload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Invalid Class Student",
			Email:          "<EMAIL>",
			PhoneNumber:    "1111111111",
			ContactAddress: "789 Invalid Street",
		},
		ParentPhone: "555-9999",
		ParentEmail: "<EMAIL>",
		Class:       "invalid_class", // Invalid class value
		Stream:      "IIT-JEE",
	}

	invalidClassBody, _ := json.Marshal(invalidClassPayload)
	invalidClassReq, _ := http.NewRequest("POST", "/api/students", bytes.NewBuffer(invalidClassBody))
	invalidClassReq.Header.Set("Content-Type", "application/json")

	invalidClassResp := httptest.NewRecorder()
	noAuthRouter.ServeHTTP(invalidClassResp, invalidClassReq)
	assert.Equal(t, http.StatusBadRequest, invalidClassResp.Code)

	// Test 4: Test validation - invalid stream value
	invalidStreamPayload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Invalid Stream Student",
			Email:          "<EMAIL>",
			PhoneNumber:    "2222222222",
			ContactAddress: "101 Invalid Stream Street",
		},
		ParentPhone: "555-8888",
		ParentEmail: "<EMAIL>",
		Class:       "12th",
		Stream:      "INVALID-STREAM", // Invalid stream value
	}

	invalidStreamBody, _ := json.Marshal(invalidStreamPayload)
	invalidStreamReq, _ := http.NewRequest("POST", "/api/students", bytes.NewBuffer(invalidStreamBody))
	invalidStreamReq.Header.Set("Content-Type", "application/json")

	invalidStreamResp := httptest.NewRecorder()
	noAuthRouter.ServeHTTP(invalidStreamResp, invalidStreamReq)
	assert.Equal(t, http.StatusBadRequest, invalidStreamResp.Code)

	// Cleanup all test data
	db.Delete(&student)
	db.Delete(&student.User)
	db.Delete(&minimalStudent)
	db.Delete(&minimalStudent.User)
}

func TestCreateAdmin(t *testing.T) {
	// Clean up before test
	db.Exec("DELETE FROM users WHERE email = ?", "<EMAIL>")

	adminPayload := models.AdminForCreate{
		FullName:       "Admin User",
		Email:          "<EMAIL>",
		PhoneNumber:    "9876543213",
		ContactAddress: "456 Admin Street",
		Password:       "adminpassword123",
	}

	body, _ := json.Marshal(adminPayload)
	req, _ := http.NewRequest("POST", "/api/admins", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	resp := httptest.NewRecorder()
	noAuthRouter.ServeHTTP(resp, req)

	assert.Equal(t, http.StatusCreated, resp.Code)

	// Parse response
	var response map[string]interface{}
	err := json.Unmarshal(resp.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.NotNil(t, response["token"])
	assert.NotNil(t, response["admin"])

	// Verify admin was created in database
	var savedAdmin models.User
	result := db.First(&savedAdmin, "email = ?", "<EMAIL>")
	assert.Nil(t, result.Error)
	assert.Equal(t, "Admin User", savedAdmin.FullName)
	assert.Equal(t, "<EMAIL>", savedAdmin.Email)
	assert.Equal(t, "9876543213", savedAdmin.PhoneNumber)
	assert.Equal(t, "Admin", savedAdmin.Role)
	assert.NotEmpty(t, savedAdmin.PasswordHash) // Password should be hashed

	// Cleanup
	db.Delete(&savedAdmin)
}
